
import React, { useState } from 'react';
import { Calendar, Clock, MapPin, Users, Video, ArrowRight } from 'lucide-react';

const Events = () => {
  const [activeTab, setActiveTab] = useState('upcoming');

  const upcomingEvents = [
    {
      id: 1,
      title: "Policy Dialogue: India's Economic Resilience Post-2024",
      date: "January 15, 2025",
      time: "10:00 AM - 12:00 PM",
      venue: "India International Centre, New Delhi",
      type: "Conference",
      description: "A comprehensive discussion on India's economic recovery strategies and policy frameworks for sustained growth in the post-pandemic era.",
      speakers: ["Dr<PERSON>", "Prof. <PERSON><PERSON>", "Hon. <PERSON> (Former Finance Minister)"],
      registrationLink: "#",
      isVirtual: false,
      capacity: "150 participants"
    },
    {
      id: 2,
      title: "Workshop: Data Governance and Privacy Laws in India",
      date: "January 22, 2025",
      time: "2:00 PM - 5:00 PM",
      venue: "Online Event",
      type: "Workshop",
      description: "An interactive workshop exploring the implications of India's data protection legislation and its impact on digital governance.",
      speakers: ["<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "Legal Expert Panel"],
      registrationLink: "#",
      isVirtual: true,
      capacity: "200 participants"
    },
    {
      id: 3,
      title: "Roundtable: Climate Finance and Green Transition",
      date: "February 5, 2025",
      time: "9:30 AM - 1:00 PM",
      venue: "The Energy and Resources Institute (TERI), New Delhi",
      type: "Roundtable",
      description: "A closed-door discussion with policymakers and industry leaders on financing India's transition to a green economy.",
      speakers: ["Climate Policy Experts", "Industry Leaders", "Government Officials"],
      registrationLink: "#",
      isVirtual: false,
      capacity: "50 participants (Invitation Only)"
    }
  ];

  const pastEvents = [
    {
      id: 4,
      title: "Annual Conference: Federalism and Governance in 21st Century India",
      date: "November 18, 2024",
      venue: "Constitution Club of India, New Delhi",
      type: "Conference",
      description: "Our flagship annual conference brought together policymakers, academics, and civil society to discuss the evolving nature of federal governance in India.",
      attendees: "300+ participants",
      outcomes: "Published proceedings and policy brief on federal governance reforms"
    },
    {
      id: 5,
      title: "Panel Discussion: Digital India and the Future of Work",
      date: "October 12, 2024",
      venue: "Online Event",
      type: "Panel Discussion",
      description: "A virtual panel exploring how digital transformation is reshaping India's labor market and the policy responses needed.",
      attendees: "500+ participants",
      outcomes: "White paper on future of work in digital India"
    },
    {
      id: 6,
      title: "Policy Workshop: Urban Governance and Smart Cities",
      date: "September 8, 2024",
      venue: "Indian Institute of Public Administration, New Delhi",
      type: "Workshop",
      description: "A hands-on workshop for urban planners and policymakers on implementing smart city initiatives effectively.",
      attendees: "120+ participants",
      outcomes: "Best practices manual for smart city implementation"
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="bg-[#1e293c] text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Events</h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              Join our policy dialogues, workshops, and conferences that bring
              together thought leaders, policymakers, and practitioners.
            </p>
          </div>
        </div>
      </section>

      {/* Event Navigation */}
      <section className="py-8 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center">
            <div className="flex bg-white rounded-lg p-1 shadow-sm">
              <button
                onClick={() => setActiveTab("upcoming")}
                className={`px-6 py-2 rounded-md font-semibold transition-colors ${
                  activeTab === "upcoming"
                    ? "bg-blue-600 text-white"
                    : "text-gray-600 hover:text-blue-600"
                }`}
              >
                Upcoming Events
              </button>
              <button
                onClick={() => setActiveTab("past")}
                className={`px-6 py-2 rounded-md font-semibold transition-colors ${
                  activeTab === "past"
                    ? "bg-blue-600 text-white"
                    : "text-gray-600 hover:text-blue-600"
                }`}
              >
                Past Events
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Upcoming Events */}
      {activeTab === "upcoming" && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Upcoming Events
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Mark your calendar for these upcoming policy discussions and
                research presentations.
              </p>
            </div>

            <div className="space-y-8">
              {upcomingEvents.map((event) => (
                <div
                  key={event.id}
                  className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden"
                >
                  <div className="p-8">
                    <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-3">
                          <span
                            className={`px-3 py-1 text-xs font-semibold rounded-full mr-3 ${
                              event.type === "Conference"
                                ? "bg-blue-100 text-blue-800"
                                : event.type === "Workshop"
                                ? "bg-green-100 text-green-800"
                                : "bg-purple-100 text-purple-800"
                            }`}
                          >
                            {event.type}
                          </span>
                          {event.isVirtual && (
                            <span className="px-3 py-1 text-xs font-semibold bg-orange-100 text-orange-800 rounded-full">
                              Virtual Event
                            </span>
                          )}
                        </div>

                        <h3 className="text-2xl font-bold text-gray-900 mb-4">
                          {event.title}
                        </h3>
                        <p className="text-gray-700 mb-6 leading-relaxed">
                          {event.description}
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                          <div className="flex items-center text-gray-600">
                            <Calendar
                              className="mr-2 flex-shrink-0"
                              size={16}
                            />
                            <span>{event.date}</span>
                          </div>
                          <div className="flex items-center text-gray-600">
                            <Clock className="mr-2 flex-shrink-0" size={16} />
                            <span>{event.time}</span>
                          </div>
                          <div className="flex items-center text-gray-600">
                            {event.isVirtual ? (
                              <Video className="mr-2 flex-shrink-0" size={16} />
                            ) : (
                              <MapPin
                                className="mr-2 flex-shrink-0"
                                size={16}
                              />
                            )}
                            <span>{event.venue}</span>
                          </div>
                          <div className="flex items-center text-gray-600">
                            <Users className="mr-2 flex-shrink-0" size={16} />
                            <span>{event.capacity}</span>
                          </div>
                        </div>

                        <div className="mb-6">
                          <h4 className="font-semibold text-gray-900 mb-2">
                            Featured Speakers:
                          </h4>
                          <ul className="text-gray-700">
                            {event.speakers.map((speaker, index) => (
                              <li
                                key={index}
                                className="flex items-center mb-1"
                              >
                                <div className="w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                                {speaker}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="lg:ml-8 lg:flex-shrink-0">
                        <button className="w-full lg:w-auto bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center justify-center">
                          Register Now
                          <ArrowRight className="ml-2" size={20} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Past Events */}
      {activeTab === "past" && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Past Events
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                A look back at our recent policy dialogues and their impact on
                public discourse.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {pastEvents.map((event) => (
                <div
                  key={event.id}
                  className="bg-white border border-gray-200 rounded-lg shadow-md overflow-hidden"
                >
                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <span
                        className={`px-3 py-1 text-xs font-semibold rounded-full ${
                          event.type === "Conference"
                            ? "bg-blue-100 text-blue-800"
                            : event.type === "Workshop"
                            ? "bg-green-100 text-green-800"
                            : "bg-purple-100 text-purple-800"
                        }`}
                      >
                        {event.type}
                      </span>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-3">
                      {event.title}
                    </h3>
                    <p className="text-gray-700 mb-4 leading-relaxed">
                      {event.description}
                    </p>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-gray-600 text-sm">
                        <Calendar className="mr-2 flex-shrink-0" size={14} />
                        <span>{event.date}</span>
                      </div>
                      <div className="flex items-center text-gray-600 text-sm">
                        <MapPin className="mr-2 flex-shrink-0" size={14} />
                        <span>{event.venue}</span>
                      </div>
                      <div className="flex items-center text-gray-600 text-sm">
                        <Users className="mr-2 flex-shrink-0" size={14} />
                        <span>{event.attendees}</span>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">
                        Outcomes:
                      </h4>
                      <p className="text-gray-700 text-sm">{event.outcomes}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Event Partnership */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Partner With Us
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Interested in co-hosting an event or speaking at one of our
              programs? We welcome collaborations.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="text-blue-600" size={24} />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Speaking Opportunities
              </h3>
              <p className="text-gray-600 mb-4">
                Share your expertise at our policy dialogues and conferences.
              </p>
              <button className="text-blue-600 font-semibold hover:text-blue-800">
                Learn More
              </button>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="text-blue-600" size={24} />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Event Partnership
              </h3>
              <p className="text-gray-600 mb-4">
                Collaborate with us to organize impactful policy events.
              </p>
              <button className="text-blue-600 font-semibold hover:text-blue-800">
                Learn More
              </button>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Video className="text-blue-600" size={24} />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Virtual Events
              </h3>
              <p className="text-gray-600 mb-4">
                Join our online discussions and webinar series.
              </p>
              <button className="text-blue-600 font-semibold hover:text-blue-800">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Events;
