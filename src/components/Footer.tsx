
import React from 'react';
import { Link } from 'react-router-dom';
import { Mail, Phone, MapPin } from 'lucide-react';
import { FaTwitter, FaFacebook, FaLinkedin } from "react-icons/fa";


const Footer = () => {
  return (
    <footer className="text-white border-t-2  bg-teal-300">
      {/* Newspaper-style decorative border */}
      <div className="h-1 "></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Organization Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="mb-6">
              <h2 className="newspaper-heading text-2xl text-white mb-2 tracking-wider uppercase">
                Delhi Chapter
              </h2>
              <div className="flex items-center space-x-2 mb-3">
              </div>
            </div>
            <p className="newspaper-body text-teal-100 mb-6 leading-relaxed text-sm">
              Delhi Chapter | Sangam Centre is a leading policy think tank dedicated to fostering 
              evidence-based policy research and promoting informed public discourse on critical 
              issues facing India and the world.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-teal-900 hover:text-white transition-colors p-2 hover:bg-teal-600 rounded">
                <FaTwitter size={30} />
              </a>
              <a href="#" className="text-teal-900 hover:text-white transition-colors p-2 hover:bg-teal-600 rounded">
                <FaLinkedin size={30} />
              </a>
              <a href="#" className="text-teal-900 hover:text-white transition-colors p-2 hover:bg-teal-600 rounded">
                <FaFacebook size={30} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="newspaper-heading text-lg text-white mb-4 uppercase tracking-wide border-b border-teal-500 pb-2">
              Sections
            </h3>
            <ul className="space-y-3">
              <li><Link to="/about" className="text-teal-900 hover:text-white transition-colors text-sm hover-underline">About Us</Link></li>
              <li><Link to="/focus-areas" className="text-teal-900 hover:text-white transition-colors text-sm hover-underline">Focus Areas</Link></li>
              <li><Link to="/publications" className="text-teal-900 hover:text-white transition-colors text-sm hover-underline">Publications</Link></li>
              <li><Link to="/events" className="text-teal-900 hover:text-white transition-colors text-sm hover-underline">Events</Link></li>
              <li><Link to="/media" className="text-teal-900 hover:text-white transition-colors text-sm hover-underline">Media</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="newspaper-heading text-lg text-white mb-4 uppercase tracking-wide border-b border-teal-500 pb-2">
              Contact
            </h3>
            <ul className="space-y-4">
              <li className="flex items-start space-x-3">
                <MapPin size={16} className="text-teal-300 mt-1 flex-shrink-0" />
                <span className="text-teal-100 text-sm newspaper-body">123 Policy Street, New Delhi, India 110001</span>
              </li>
              <li className="flex items-center space-x-3">
                <Phone size={16} className="text-teal-300 flex-shrink-0" />
                <span className="text-teal-100 text-sm newspaper-body">+91 11 2345 6789</span>
              </li>
              <li className="flex items-center space-x-3">
                <Mail size={16} className="text-teal-300 flex-shrink-0" />
                <span className="text-teal-100 text-sm newspaper-body"><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom section with newspaper-style divider */}
        <div className="border-t border-teal-500 mt-10 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-teal-200 text-sm newspaper-body">
              © 2024 Delhi Chapter | Sangam Centre. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
